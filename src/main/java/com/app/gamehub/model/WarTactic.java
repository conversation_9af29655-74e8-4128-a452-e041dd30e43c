package com.app.gamehub.model;

import com.app.gamehub.entity.GameAccount;
import com.app.gamehub.entity.WarArrangement;
import com.app.gamehub.entity.WarGroup;
import com.app.gamehub.entity.WarType;
import com.app.gamehub.exception.BusinessException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;

@Getter
@RequiredArgsConstructor
public enum WarTactic {
  /** 抱粮守仓：保己方三个小粮仓 */
  HOLD_GRAIN(Set.of(WarType.GUANDU_ONE, WarType.GUANDU_TWO)) {
    @Override
    protected List<TacticalArrangement> arrangementSorted(List<GameAccount> accounts) {
      List<TacticalArrangement> arrangements = new ArrayList<>();

      // 初始化三个小组
      for (int i = 0; i < 3; i++) {
        TacticalArrangement arrangement = new TacticalArrangement();
        WarGroup warGroup = new WarGroup();
        arrangement.setWarGroup(warGroup);
        arrangement.setWarArrangements(new ArrayList<>());
        arrangements.add(arrangement);
      }

      // 分配账号到小组
      // 分为三个小组，游戏账号已经根据账号伤害加成从大到小排列，加成排 1 2 3 的分别为三个队的组长，分别为第一、二、三组
      // 加成第 4 5 6的安排在第三二一组
      // 加成第 7 8 9 的安排在第一二三组
      // 以此类推，直到游戏账号全部安排完
      for (int i = 0; i < accounts.size(); i++) {
        int groupIndex;
        int positionInCycle = i % 6;

        if (positionInCycle < 3) {
          // 前3个：1->0, 2->1, 3->2
          groupIndex = positionInCycle;
        } else {
          // 后3个：4->2, 5->1, 6->0 (第三二一组)
          groupIndex = 5 - positionInCycle;
        }

        GameAccount account = accounts.get(i);
        TacticalArrangement arrangement = arrangements.get(groupIndex);

        // 创建WarArrangement
        WarArrangement warArrangement = new WarArrangement();
        warArrangement.setAccountId(account.getId());
        warArrangement.setAccount(account);
        arrangement.getWarArrangements().add(warArrangement);
      }

      // 设置小组名称和任务
      String[] tasks = {
        "拿下我方一号小粮仓（阳武或封丘，靠近大本营的为我方）",
        "拿下我方三号小粮仓",
        "拿下我方二号小粮仓（原武或燕县，靠近大本营的为我方）"
      };

      for (int i = 0; i < arrangements.size(); i++) {
        TacticalArrangement arrangement = arrangements.get(i);
        if (!arrangement.getWarArrangements().isEmpty()) {
          // 找到加成最高的账号（第一个账号就是加成最高的）
          GameAccount leader = arrangement.getWarArrangements().get(0).getAccount();
          String groupName = leader.getAccountName() + "的小分队";

          arrangement.getWarGroup().setGroupName(groupName);
          arrangement.getWarGroup().setGroupTask(tasks[i]);
        }
      }

      return arrangements;
    }
  },
  /** 断粮抢米：保住己方小粮仓的同时拿下敌方三个小粮仓，放弃兵器坊和工匠坊。 */
  CUT_SUPPLY(Set.of(WarType.GUANDU_ONE, WarType.GUANDU_TWO)) {
    @Override
    protected List<TacticalArrangement> arrangementSorted(List<GameAccount> accounts) {
      List<TacticalArrangement> arrangements = new ArrayList<>();

      // 初始化六个小组
      for (int i = 0; i < 6; i++) {
        TacticalArrangement arrangement = new TacticalArrangement();
        WarGroup warGroup = new WarGroup();
        arrangement.setWarGroup(warGroup);
        arrangement.setWarArrangements(new ArrayList<>());
        arrangements.add(arrangement);
      }

      // 分配规则：
      // 第一组：1 6 10 18 22 (索引: 0 5 9 17 21)
      // 第二组：2 5 11 17 23 (索引: 1 4 10 16 22)
      // 第三组：3 4 12 16 24 (索引: 2 3 11 15 23)
      // 第四组：7 15 19 27 28 (索引: 6 14 18 26 27)
      // 第五组：8 14 20 26 29 (索引: 7 13 19 25 28)
      // 第六组：9 13 21 25 30 (索引: 8 12 20 24 29)

      int[][] groupAssignments = {
        {0, 5, 9, 17, 21},   // 第一组
        {1, 4, 10, 16, 22},  // 第二组
        {2, 3, 11, 15, 23},  // 第三组
        {6, 14, 18, 26, 27}, // 第四组
        {7, 13, 19, 25, 28}, // 第五组
        {8, 12, 20, 24, 29}  // 第六组
      };

      for (int groupIndex = 0; groupIndex < groupAssignments.length; groupIndex++) {
        for (int accountIndex : groupAssignments[groupIndex]) {
          if (accountIndex < accounts.size()) {
            GameAccount account = accounts.get(accountIndex);
            TacticalArrangement arrangement = arrangements.get(groupIndex);

            WarArrangement warArrangement = new WarArrangement();
            warArrangement.setAccountId(account.getId());
            warArrangement.setAccount(account);
            arrangement.getWarArrangements().add(warArrangement);
          }
        }
      }

      // 设置小组名称和任务
      String[] tasks = {
        "拿下敌方一号小粮仓（阳武或封丘，靠近大本营的为我方），二队驻防敌方三号小粮仓，三队驻防敌方二号小粮仓（原武或燕县，靠近大本营的为我方）",
        "拿下我方一号小粮仓（阳武或封丘，靠近大本营的为我方），二队驻防我方三号小粮仓，三队驻防我方二号小粮仓（原武或燕县，靠近大本营的为我方）",
        "拿下敌方三号小粮仓，二队驻防敌方二号小粮仓（原武或燕县，靠近大本营的为我方），三队驻防敌方一号小粮仓（阳武或封丘，靠近大本营的为我方）",
        "拿下我方三号小粮仓，二队驻防我方二号小粮仓（原武或燕县，靠近大本营的为我方），三队驻防我方一号小粮仓（阳武或封丘，靠近大本营的为我方）",
        "拿下敌方二号小粮仓（原武或燕县，靠近大本营的为我方），二队驻防敌方一号小粮仓（阳武或封丘，靠近大本营的为我方），三队驻防敌方三号小粮仓",
        "拿下我方二号小粮仓（原武或燕县，靠近大本营的为我方），二队驻防我方一号小粮仓（阳武或封丘，靠近大本营的为我方），三队驻防我方三号小粮仓"
      };

      for (int i = 0; i < arrangements.size(); i++) {
        TacticalArrangement arrangement = arrangements.get(i);
        if (!arrangement.getWarArrangements().isEmpty()) {
          // 找到加成最高的账号（按分配顺序，第一个就是加成最高的）
          GameAccount leader = arrangement.getWarArrangements().get(0).getAccount();
          String groupName = leader.getAccountName() + "的小分队";

          arrangement.getWarGroup().setGroupName(groupName);
          arrangement.getWarGroup().setGroupTask(tasks[i]);
        }
      }

      return arrangements;
    }
  },
  /** 多点开花：主拿己方上下两个小粮仓，派兵拿下己方中间粮仓，拿下兵器坊和工匠坊，拿下敌方上下两个粮仓。 */
  MULTI_ATTACK(Set.of(WarType.GUANDU_ONE, WarType.GUANDU_TWO)) {
    @Override
    protected List<TacticalArrangement> arrangementSorted(List<GameAccount> accounts) {
      List<TacticalArrangement> arrangements = new ArrayList<>();

      // 初始化六个小组
      for (int i = 0; i < 6; i++) {
        TacticalArrangement arrangement = new TacticalArrangement();
        WarGroup warGroup = new WarGroup();
        arrangement.setWarGroup(warGroup);
        arrangement.setWarArrangements(new ArrayList<>());
        arrangements.add(arrangement);
      }

      // 分配规则：
      // 第一组：1 4 5 19 22 (索引: 0 3 4 18 21)
      // 第二组：2 3 6 20 21 (索引: 1 2 5 19 20)
      // 第三组：7 14 15 26 27 (索引: 6 13 14 25 26)
      // 第四组：8 13 16 25 28 (索引: 7 12 15 24 27)
      // 第五组：9 12 17 24 29 (索引: 8 11 16 23 28)
      // 第六组：10 11 18 23 30 (索引: 9 10 17 22 29)

      int[][] groupAssignments = {
        {0, 3, 4, 18, 21},   // 第一组
        {1, 2, 5, 19, 20},   // 第二组
        {6, 13, 14, 25, 26}, // 第三组
        {7, 12, 15, 24, 27}, // 第四组
        {8, 11, 16, 23, 28}, // 第五组
        {9, 10, 17, 22, 29}  // 第六组
      };

      for (int groupIndex = 0; groupIndex < groupAssignments.length; groupIndex++) {
        for (int accountIndex : groupAssignments[groupIndex]) {
          if (accountIndex < accounts.size()) {
            GameAccount account = accounts.get(accountIndex);
            TacticalArrangement arrangement = arrangements.get(groupIndex);

            WarArrangement warArrangement = new WarArrangement();
            warArrangement.setAccountId(account.getId());
            warArrangement.setAccount(account);
            arrangement.getWarArrangements().add(warArrangement);
          }
        }
      }

      // 设置小组名称和任务
      String[] tasks = {
        "拿下兵器坊，二队驻防敌方一号小粮仓（阳武或封丘，靠近大本营的为我方），三队驻防我方一号小粮仓（阳武或封丘，靠近大本营的为我方），四队驻防工匠坊",
        "拿下工匠坊，二队驻防敌方三号小粮仓，三队驻防我方三号小粮仓，四队驻防兵器坊",
        "拿下敌方一号小粮仓（阳武或封丘，靠近大本营的为我方），二队驻防兵器坊，三队驻防敌方三号小粮仓，四队驻防工匠坊",
        "拿下敌方三号小粮仓，二队驻防工匠坊，三队驻防敌方一号小粮仓（阳武或封丘，靠近大本营的为我方），四队驻防兵器坊",
        "拿下我方一号小粮仓（阳武或封丘，靠近大本营的为我方），二队驻防兵器坊，三队驻防我方三号小粮仓，四队驻防工匠坊",
        "拿下我方三号小粮仓，二队驻防工匠坊，三队驻防我方一号小粮仓（阳武或封丘，靠近大本营的为我方），四队驻防兵器坊"
      };

      for (int i = 0; i < arrangements.size(); i++) {
        TacticalArrangement arrangement = arrangements.get(i);
        if (!arrangement.getWarArrangements().isEmpty()) {
          // 找到加成最高的账号（按分配顺序，第一个就是加成最高的）
          GameAccount leader = arrangement.getWarArrangements().get(0).getAccount();
          String groupName = leader.getAccountName() + "的小分队";

          arrangement.getWarGroup().setGroupName(groupName);
          arrangement.getWarGroup().setGroupTask(tasks[i]);
        }
      }

      return arrangements;
    }
  },
  /** 顺手牵羊：拿下己方三个小粮仓，拿下兵器坊和敌方上方的小粮仓。 */
  STEAL_POINT(Set.of(WarType.GUANDU_ONE, WarType.GUANDU_TWO)) {
    @Override
    protected List<TacticalArrangement> arrangementSorted(List<GameAccount> accounts) {
      List<TacticalArrangement> arrangements = new ArrayList<>();

      // 初始化六个小组
      for (int i = 0; i < 6; i++) {
        TacticalArrangement arrangement = new TacticalArrangement();
        WarGroup warGroup = new WarGroup();
        arrangement.setWarGroup(warGroup);
        arrangement.setWarArrangements(new ArrayList<>());
        arrangements.add(arrangement);
      }

      // 分配规则：
      // 第一组：1 3 10 17 23 (索引: 0 2 9 16 22)
      // 第二组：2 4 11 16 22 (索引: 1 3 10 15 21)
      // 第三组：5 6 12 18 24 (索引: 4 5 11 17 23)
      // 第四组：9 13 21 27 28 (索引: 8 12 20 26 27)
      // 第五组：8 15 20 25 29 (索引: 7 14 19 24 28)
      // 第六组：7 14 19 26 30 (索引: 6 13 18 25 29)

      int[][] groupAssignments = {
        {0, 2, 9, 16, 22},   // 第一组
        {1, 3, 10, 15, 21},  // 第二组
        {4, 5, 11, 17, 23},  // 第三组
        {8, 12, 20, 26, 27}, // 第四组
        {7, 14, 19, 24, 28}, // 第五组
        {6, 13, 18, 25, 29}  // 第六组
      };

      for (int groupIndex = 0; groupIndex < groupAssignments.length; groupIndex++) {
        for (int accountIndex : groupAssignments[groupIndex]) {
          if (accountIndex < accounts.size()) {
            GameAccount account = accounts.get(accountIndex);
            TacticalArrangement arrangement = arrangements.get(groupIndex);

            WarArrangement warArrangement = new WarArrangement();
            warArrangement.setAccountId(account.getId());
            warArrangement.setAccount(account);
            arrangement.getWarArrangements().add(warArrangement);
          }
        }
      }

      // 设置小组名称和任务
      String[] tasks = {
        "拿下兵器坊，二队驻防敌方一号小粮仓（阳武或封丘，靠近大本营的为我方），三队驻防我方一号小粮仓（阳武或封丘，靠近大本营的为我方）",
        "拿下工匠坊，二队驻防我方三号小粮仓，三队驻防我方二号小粮仓（原武或燕县，靠近大本营的为我方）",
        "拿下敌方一号小粮仓（阳武或封丘，靠近大本营的为我方），二队可以尝试集结敌方二号小粮仓（原武或燕县，靠近大本营的为我方）或者驻防兵器坊，三队驻防工匠坊",
        "拿下我方一号小粮仓（阳武或封丘，靠近大本营的为我方），主要驻防兵器坊和二号小粮仓（原武或燕县，靠近大本营的为我方）",
        "拿下我方三号小粮仓，二队驻防工匠坊，三队驻防我方二号小粮仓（原武或燕县，靠近大本营的为我方）",
        "拿下我方二号小粮仓（原武或燕县，靠近大本营的为我方），二队驻防我方三号小粮仓，三队驻防兵器坊"
      };

      for (int i = 0; i < arrangements.size(); i++) {
        TacticalArrangement arrangement = arrangements.get(i);
        if (!arrangement.getWarArrangements().isEmpty()) {
          // 找到加成最高的账号（按分配顺序，第一个就是加成最高的）
          GameAccount leader = arrangement.getWarArrangements().get(0).getAccount();
          String groupName = leader.getAccountName() + "的小分队";

          arrangement.getWarGroup().setGroupName(groupName);
          arrangement.getWarGroup().setGroupTask(tasks[i]);
        }
      }

      return arrangements;
    }
  },
  ;

  public List<TacticalArrangement> arrangement(List<GameAccount> accounts) {
    if (CollectionUtils.isEmpty(accounts)) {
      return new ArrayList<>();
    }
    accounts.sort((a1, a2) -> a2.getDamageBonus().compareTo(a1.getDamageBonus()));
    if (accounts.size() > 30) {
      accounts = accounts.subList(0, 30);
    }
    return arrangementSorted(accounts);
  }

  protected List<TacticalArrangement> arrangementSorted(List<GameAccount> accounts) {
    throw new BusinessException("该战术暂未支持");
  }

  /**
   * 战术支持的战事
   */
  private final Set<WarType> supportedWarTypes;

}
